# Samsung Health SDK Integration Guide

This document provides a comprehensive guide for integrating Samsung Health SDK into your Flutter project to access health data from Samsung devices.

## Overview

The implementation provides a platform-specific health service architecture that automatically detects Samsung devices and uses Samsung Health SDK when available, falling back to Health Connect for other Android devices.

## Current Implementation Status

### ✅ Completed
- Flutter-side architecture with `SamsungHealthService` class
- Method channel setup for Flutter-Android communication
- Android MainActivity updated with Samsung Health method channel handlers
- Basic Samsung Health handler structure
- Permissions added to AndroidManifest.xml
- Automatic platform detection and service selection

### 🔄 Next Steps Required

## 1. Download Samsung Health Data SDK

1. Visit [Samsung Developer Portal](https://developer.samsung.com/health/data/overview.html)
2. Download the Samsung Health Data SDK v1.0.0 beta2
3. Extract the SDK files

## 2. Add Samsung Health SDK to Android Project

1. Create a `libs` folder in `android/app/` if it doesn't exist
2. Copy the Samsung Health SDK `.aar` files to `android/app/libs/`
3. Update `android/app/build.gradle`:

```gradle
dependencies {
    implementation platform('com.google.firebase:firebase-bom:32.1.0')
    implementation 'com.google.firebase:firebase-auth'
    
    // Samsung Health SDK
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation 'androidx.health.connect:connect-client:1.1.0-alpha07'
}
```

## 3. Update SamsungHealthHandler.kt

Replace the placeholder implementation in `SamsungHealthHandler.kt` with actual Samsung Health SDK calls:

```kotlin
import com.samsung.android.sdk.healthdata.*
import com.samsung.android.sdk.healthdata.HealthConstants.*

class SamsungHealthHandler(private val activity: Activity) {
    private var healthDataStore: HealthDataStore? = null
    
    fun initialize(result: MethodChannel.Result) {
        try {
            // Initialize Samsung Health SDK
            HealthDataService.initialize(activity)
            
            // Create health data store
            val connectionListener = object : HealthDataStore.ConnectionListener {
                override fun onConnected() {
                    Log.d(TAG, "Samsung Health connected")
                    result.success(true)
                }
                
                override fun onConnectionFailed(error: HealthConnectionErrorResult) {
                    Log.e(TAG, "Samsung Health connection failed: ${error.errorCode}")
                    result.error("CONNECTION_FAILED", "Failed to connect to Samsung Health", error.errorCode.toString())
                }
                
                override fun onDisconnected() {
                    Log.d(TAG, "Samsung Health disconnected")
                }
            }
            
            healthDataStore = HealthDataStore(activity, connectionListener)
            healthDataStore?.connectService()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Samsung Health SDK", e)
            result.error("INIT_ERROR", "Failed to initialize Samsung Health SDK", e.message)
        }
    }
    
    // Implement other methods with actual Samsung Health SDK calls
}
```

## 4. Implement Data Fetching Methods

For each health data type, implement the corresponding Samsung Health SDK calls:

### Steps Data
```kotlin
fun fetchStepsOnly(result: MethodChannel.Result) {
    val request = ReadDataRequest.Builder()
        .setDataType(StepCount.HEALTH_DATA_TYPE)
        .setLocalTimeRange(
            StepCount.START_TIME, StepCount.TIME_OFFSET,
            startTime, endTime
        )
        .build()
        
    healthDataStore?.readData(request) { readResult ->
        // Process step data
        var totalSteps = 0
        readResult.iterator().forEach { data ->
            totalSteps += data.getInt(StepCount.COUNT)
        }
        result.success(totalSteps)
    }
}
```

### Heart Rate Data
```kotlin
fun fetchHeartRateOnly(result: MethodChannel.Result) {
    val request = ReadDataRequest.Builder()
        .setDataType(HeartRate.HEALTH_DATA_TYPE)
        .setLocalTimeRange(
            HeartRate.START_TIME, HeartRate.TIME_OFFSET,
            startTime, endTime
        )
        .build()
        
    healthDataStore?.readData(request) { readResult ->
        // Process heart rate data
        var latestHeartRate: Float? = null
        readResult.iterator().forEach { data ->
            latestHeartRate = data.getFloat(HeartRate.HEART_RATE)
        }
        result.success(latestHeartRate?.toDouble())
    }
}
```

## 5. Handle Permissions

Samsung Health requires explicit user permissions. Implement permission handling:

```kotlin
private fun requestPermissions() {
    val permissionManager = HealthPermissionManager(healthDataStore)
    val permissions = setOf(
        PermissionKey(StepCount.HEALTH_DATA_TYPE, PermissionType.READ),
        PermissionKey(HeartRate.HEALTH_DATA_TYPE, PermissionType.READ),
        PermissionKey(Exercise.HEALTH_DATA_TYPE, PermissionType.READ),
        PermissionKey(Sleep.HEALTH_DATA_TYPE, PermissionType.READ)
    )
    
    permissionManager.requestPermissions(permissions, activity) { permissionResult ->
        // Handle permission result
    }
}
```

## 6. Error Handling and Fallback

The current implementation includes automatic fallback to Health Connect if Samsung Health is not available. This ensures compatibility across all Android devices.

## 7. Testing

1. Test on Samsung devices with Samsung Health installed
2. Test on non-Samsung devices to ensure Health Connect fallback works
3. Test permission flows and data retrieval

## 8. Data Types Supported

The Samsung Health SDK supports:
- Steps count
- Heart rate
- Sleep data
- Exercise data
- Calories burned
- Distance traveled
- Blood pressure
- Blood glucose
- Body composition

## 9. Important Notes

- Samsung Health SDK requires Samsung Health app v6.29 or later
- The SDK doesn't support emulators
- Data is for fitness and wellness only, not medical diagnosis
- Requires Android 10 (API level 29) or above

## 10. Resources

- [Samsung Health Data SDK Documentation](https://developer.samsung.com/health/data/overview.html)
- [Samsung Health SDK Code Labs](https://developer.samsung.com/codelab/health/steps-data.html)
- [Samsung Developer Support](https://developer.samsung.com/dev-support)

## Architecture Benefits

This implementation provides:
- **Automatic platform detection**: Uses Samsung Health on Samsung devices, Health Connect elsewhere
- **Unified interface**: Same Flutter API regardless of underlying health service
- **Graceful fallback**: Continues working even if Samsung Health is unavailable
- **Extensible design**: Easy to add more health data types
- **Error handling**: Comprehensive error handling and logging
