import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Simple test screen to verify Samsung Health method channel is working
class TestMethodChannelScreen extends StatefulWidget {
  const TestMethodChannelScreen({Key? key}) : super(key: key);

  @override
  State<TestMethodChannelScreen> createState() => _TestMethodChannelScreenState();
}

class _TestMethodChannelScreenState extends State<TestMethodChannelScreen> {
  static const MethodChannel _channel = MethodChannel('samsung_health_channel');
  String _result = "Not tested";
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Method Channel Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Samsung Health Method Channel Test',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            
            Text('Result: $_result'),
            const SizedBox(height: 20),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              ElevatedButton(
                onPressed: _testMethodChannel,
                child: const Text('Test Method Channel'),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _testMethodChannel() async {
    setState(() {
      _isLoading = true;
      _result = "Testing...";
    });

    try {
      log("Testing Samsung Health method channel...");
      final bool result = await _channel.invokeMethod('isSamsungHealthAvailable');
      log("Method channel result: $result");
      
      setState(() {
        _result = "Success! Samsung Health available: $result";
        _isLoading = false;
      });
    } catch (e) {
      log("Method channel error: $e");
      setState(() {
        _result = "Error: $e";
        _isLoading = false;
      });
    }
  }
}
