import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:healo/providers/health_provider.dart';

/// Test screen to verify Samsung Health SDK integration
class TestSamsungHealthScreen extends StatefulWidget {
  const TestSamsungHealthScreen({Key? key}) : super(key: key);

  @override
  State<TestSamsungHealthScreen> createState() => _TestSamsungHealthScreenState();
}

class _TestSamsungHealthScreenState extends State<TestSamsungHealthScreen> {
  String _status = "Not tested";
  bool _isLoading = false;
  Map<String, dynamic> _testResults = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Samsung Health Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Samsung Health SDK Integration Test',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            
            Text('Status: $_status'),
            const SizedBox(height: 20),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              ElevatedButton(
                onPressed: _testSamsungHealth,
                child: const Text('Test Samsung Health Integration'),
              ),
            
            const SizedBox(height: 20),
            
            if (_testResults.isNotEmpty) ...[
              const Text(
                'Test Results:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _testResults.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Text('${entry.key}: ${entry.value}'),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _testSamsungHealth() async {
    setState(() {
      _isLoading = true;
      _status = "Testing...";
      _testResults.clear();
    });

    try {
      // Test 1: Check if Samsung Health service can be created
      log("Testing Samsung Health service creation...");
      final samsungHealthService = SamsungHealthService();
      _testResults['Samsung Health Service Created'] = 'Success';

      // Test 2: Check if Samsung Health is available
      log("Testing Samsung Health availability...");
      final isAvailable = await samsungHealthService.isSamsungHealthAvailable();
      _testResults['Samsung Health Available'] = isAvailable ? 'Yes' : 'No';

      // Test 3: Test optimal platform service creation
      log("Testing optimal platform service creation...");
      final optimalService = await PlatformHealthService.createOptimal();
      _testResults['Optimal Service Type'] = optimalService.runtimeType.toString();

      // Test 4: Test service initialization
      log("Testing service initialization...");
      try {
        await optimalService.initialize();
        _testResults['Service Initialization'] = 'Success';
      } catch (e) {
        _testResults['Service Initialization'] = 'Failed: $e';
      }

      // Test 5: Test health data availability check
      log("Testing health data availability...");
      try {
        final dataAvailable = await optimalService.isHealthDataAvailable();
        _testResults['Health Data Available'] = dataAvailable ? 'Yes' : 'No';
      } catch (e) {
        _testResults['Health Data Available'] = 'Error: $e';
      }

      // Test 6: Test data fetching (this might fail if no permissions)
      log("Testing data fetching...");
      try {
        final healthData = await optimalService.fetchHealthData();
        _testResults['Steps Data'] = healthData.steps?.toString() ?? 'null';
        _testResults['Heart Rate Data'] = healthData.heartRate?.toString() ?? 'null';
        _testResults['Calories Data'] = healthData.calories?.toString() ?? 'null';
        _testResults['Distance Data'] = healthData.distance?.toString() ?? 'null';
        _testResults['Sleep Data'] = healthData.sleepHours?.toString() ?? 'null';
      } catch (e) {
        _testResults['Data Fetching'] = 'Error: $e';
      }

      setState(() {
        _status = "Test completed";
        _isLoading = false;
      });

    } catch (e) {
      log("Error during Samsung Health test: $e");
      setState(() {
        _status = "Test failed: $e";
        _isLoading = false;
        _testResults['Error'] = e.toString();
      });
    }
  }
}
