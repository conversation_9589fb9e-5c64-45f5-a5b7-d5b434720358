package com.example.healo

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.util.Log
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterFragmentActivity() {
    private val SAMSUNG_HEALTH_CHANNEL = "samsung_health_channel"
    private val SAMSUNG_HEALTH_PACKAGE = "com.sec.android.app.shealth"

    private lateinit var samsungHealthHandler: SamsungHealthHandler

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Handle the privacy policy intent from Health Connect
        if (intent?.action == "android.intent.action.VIEW_PERMISSION_USAGE") {
            // Open the privacy policy URL directly
            val privacyPolicyUrl = "https://thehelthy.co/privacy-policy"
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(privacyPolicyUrl))
            startActivity(browserIntent)

            // Note: We're not finishing the activity because we want the user
            // to be able to return to the app after viewing the privacy policy
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Initialize Samsung Health handler
        samsungHealthHandler = SamsungHealthHandler(this)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SAMSUNG_HEALTH_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "isSamsungHealthAvailable" -> {
                    result.success(isSamsungHealthInstalled())
                }
                "initialize" -> {
                    samsungHealthHandler.initialize(result)
                }
                "isHealthDataAvailable" -> {
                    samsungHealthHandler.isHealthDataAvailable(result)
                }
                "fetchHealthData" -> {
                    samsungHealthHandler.fetchHealthData(result)
                }
                "fetchStepsOnly" -> {
                    samsungHealthHandler.fetchStepsOnly(result)
                }
                "fetchHeartRateOnly" -> {
                    samsungHealthHandler.fetchHeartRateOnly(result)
                }
                "fetchCaloriesOnly" -> {
                    samsungHealthHandler.fetchCaloriesOnly(result)
                }
                "fetchSleepData" -> {
                    samsungHealthHandler.fetchSleepData(result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun isSamsungHealthInstalled(): Boolean {
        return try {
            packageManager.getPackageInfo(SAMSUNG_HEALTH_PACKAGE, 0)
            Log.d("SamsungHealth", "Samsung Health app is installed")
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.d("SamsungHealth", "Samsung Health app is not installed")
            false
        }
    }
}
